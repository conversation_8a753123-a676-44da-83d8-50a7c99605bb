"""LinkedIn profiles search tool."""

from typing import List, Union, Dict, Any, Tuple, Optional, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langgraph.config import get_stream_writer
from bond_ai.configuration import Configuration
import os
import requests

from ...linkedin_filters import FilterType as LinkedInFilterType, PersonSearchRequest
from ...agent_db import get_table_row_count, create_table_row, save_linkedin_profiles_to_separate_columns, create_linkedin_profile_columns

#CRUSTDATA_API_KEY = os.getenv("CRUSTDATA_API_KEY")
LIMA_API_KEY = os.getenv("LIMA_API_KEY")
    
@tool
def search_linkedin_profiles(
    config: Annotated[RunnableConfig, InjectedToolArg],
    filters: List[Union[LinkedInFilterType, Dict[str, Any]]],
    page: int = 1,
) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Search for LinkedIn profiles using CrustData API and save results to table.
    
    This tool searches for LinkedIn profiles based on specified filters and automatically
    saves the results to the current table in separate columns (Full Name, Job Title, 
    Company Name, LinkedIn URL).
    
    Parameters:
        config: Configuration injected by the system
        filters: List of search filters to apply. Each filter should specify the field,
                operator, and value(s) to filter by.
        page: Page number for pagination (default: 1)
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: Tuple containing (search_results, error)
        where search_results contains information about the search and saved profiles,
        None if failed, and error is the error message if failed, None if successful
    """
    try:
        if not LIMA_API_KEY:
            return None, "Error: LIMA_API_KEY environment variable is not set."
        
        # Get configuration
        configuration = Configuration.from_runnable_config(config)
       
        table_id = configuration.table_id
        
        # Process filters to ensure they're in the correct format
        processed_filters = []
        for filter_item in filters:
            if isinstance(filter_item, dict):
                # Already a dictionary, use as-is
                processed_filters.append(filter_item)
            else:
                # Convert FilterType object to dictionary
                processed_filters.append(filter_item.model_dump(mode='json'))
        
        # Create or get the LinkedIn profile columns
        column_ids, column_error = create_linkedin_profile_columns(table_id)
        if column_error:
            return None, f"Error creating LinkedIn profile columns: {column_error}"
        
        # Ensure table has at least 25 rows for the search results
        row_count, row_count_error = get_table_row_count(table_id)
        if row_count_error:
            return None, f"Error checking table row count: {row_count_error}"
        
        # If table has fewer than 25 rows, create the needed rows
        if row_count < 25:
            rows_to_create = 25 - row_count
            print(f"Table has {row_count} rows, creating {rows_to_create} additional rows to reach 25")
            
            for i in range(rows_to_create):
                # Index should be current row_count + i + 1 (1-based indexing)
                index = row_count + i + 1
                create_row_result, create_row_error = create_table_row(table_id, index)
                if create_row_error:
                    return None, f"Error creating row {index}: {create_row_error}"
            
            print(f"Successfully created {rows_to_create} rows")
        else:
            print(f"Table already has {row_count} rows (>= 25), no additional rows needed")
        
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": "Searching LinkedIn Profiles"})
        
        # Create the request model with processed filters and page
        search_request = PersonSearchRequest(filters=processed_filters, page=page)
        
        # Convert to dictionary for the API call
        payload = search_request.model_dump(mode='json')
        print("[Debug] Payload", payload )
        # Make the API request
        headers = {
            'Content-Type': 'application/json',
            #'Accept': 'application/json, text/plain, */*',
            #'Authorization': f'Token {LIMA_API_KEY}'
            'X-Api-Key': f'{LIMA_API_KEY}'
        }
        url = "https://api.limadata.com/api/v2/prospect/live/people/filter"
        
        return None, f"TEST - PLEASE ROUTE TO END no need to retry"
        response = requests.post(url,headers=headers,json=payload)
        # print("[Debug] Response api", response.json() )
        
        # print("[Debug] Response api status code",response.status_code )
        return None, f"TEST - PLEASE ROUTE TO END no need to retry"
        #mockup response for testing
        if response.status_code == 402 or response.status_code == 400:
            data = {
                "profiles": [
                    {
                        "name": "Fiona Carney",
                        "headline": "COO at Microsoft EMEA",
                        "current_company": "Microsoft",
                        "current_title": "Chief Operating Officer, EMEA",
                        "linkedin_profile_url": "https://www.linkedin.com/in/fionacarney"
                    },
                    {
                        "name": "Rajesh Patel",
                        "headline": "CTO • Google Cloud APAC",
                        "current_company": "Google",
                        "current_title": "Chief Technology Officer, APAC",
                        "linkedin_profile_url": "https://www.linkedin.com/in/rajesh-patel-cto"
                    },
                    {
                        "name": "María González",
                        "headline": "VP Sales LATAM at Salesforce",
                        "current_company": "Salesforce",
                        "current_title": "Vice President, Sales – LATAM",
                        "linkedin_profile_url": "https://www.linkedin.com/in/mariagonzalez-sales"
                    },
                    {
                        "name": "Chloé Dubois",
                        "headline": "Head of AI Research at DeepMind",
                        "current_company": "DeepMind",
                        "current_title": "Head of AI Research, Europe",
                        "linkedin_profile_url": "https://www.linkedin.com/in/chloedubois-ai"
                    },
                    {
                        "name": "Lukas Müller",
                        "headline": "CFO at Siemens Mobility",
                        "current_company": "Siemens",
                        "current_title": "Chief Financial Officer, Mobility",
                        "linkedin_profile_url": "https://www.linkedin.com/in/lukas-mueller-cfo"
                    },
                    {
                        "name": "Amina Hassan",
                        "headline": "Director of Product • Spotify MENA",
                        "current_company": "Spotify",
                        "current_title": "Director of Product, MENA",
                        "linkedin_profile_url": "https://www.linkedin.com/in/amina-hassan-product"
                    },
                    {
                        "name": "Jacob Lee",
                        "headline": "Senior Data Scientist at Netflix",
                        "current_company": "Netflix",
                        "current_title": "Senior Data Scientist",
                        "linkedin_profile_url": "https://www.linkedin.com/in/jacoblee-datascience"
                    },
                    {
                        "name": "Sofia Rossi",
                        "headline": "CMO at Gucci",
                        "current_company": "Gucci",
                        "current_title": "Chief Marketing Officer",
                        "linkedin_profile_url": "https://www.linkedin.com/in/sofiarossi-cmo"
                    },
                    {
                        "name": "Daniel Kim",
                        "headline": "Global Head of Strategy • Tesla",
                        "current_company": "Tesla",
                        "current_title": "Global Head of Strategy",
                        "linkedin_profile_url": "https://www.linkedin.com/in/danielkim-strategy"
                    },
                    {
                        "name": "Laura Smith",
                        "headline": "VP People Ops at Airbnb",
                        "current_company": "Airbnb",
                        "current_title": "Vice President, People Operations",
                        "linkedin_profile_url": "https://www.linkedin.com/in/laurasmith-peopleops"
                    }
                ],
                "total_display_count": 78000
            }
               # Save profiles to separate columns if we have profiles in the response
            profiles_saved_count = 0
            if 'profiles' in data and data['profiles']:
                save_success, save_error = save_linkedin_profiles_to_separate_columns(table_id, data['profiles'], column_ids)
                if save_error:
                    print(f"Warning: Failed to save profiles to columns: {save_error}")
                else:
                    profiles_saved_count = len(data['profiles'])
                    print(f"Successfully saved {profiles_saved_count} profiles to separate columns")
            
            # Construct informative response for the agent (without returning actual profiles)
            total_display_count = data.get('total_display_count', 0)
            
            response_data = {
                "message": f"LinkedIn search completed successfully. Found {total_display_count} total profiles matching your criteria. Saved {profiles_saved_count} profiles to the table across 4 columns (Full Name, Job Title, Company Name, LinkedIn URL).",
                "total_display_count": total_display_count,
                "profiles_saved_to_table": profiles_saved_count,
                "columns_created": list(column_ids.keys()),
                "column_ids": column_ids
            }
            
            return response_data, None
        # Check response status
        if response.status_code == 200:
            data = response.json()
            
            # Save profiles to separate columns if we have profiles in the response
            profiles_saved_count = 0
            if 'profiles' in data and data['profiles']:
                save_success, save_error = save_linkedin_profiles_to_separate_columns(table_id, data['profiles'], column_ids)
                if save_error:
                    print(f"Warning: Failed to save profiles to columns: {save_error}")
                else:
                    profiles_saved_count = len(data['profiles'])
                    print(f"Successfully saved {profiles_saved_count} profiles to separate columns")
            
            # Construct informative response for the agent (without returning actual profiles)
            total_display_count = data.get('total_display_count', 0)
            
            response_data = {
                "message": f"LinkedIn search completed successfully. Found {total_display_count} total profiles matching your criteria. Saved {profiles_saved_count} profiles to the table across 4 columns (Full Name, Job Title, Company Name, LinkedIn URL).",
                "total_display_count": total_display_count,
                "profiles_saved_to_table": profiles_saved_count,
                "columns_created": list(column_ids.keys()),
                "column_ids": column_ids
            }
            
            return response_data, None
        elif response.status_code == 401:
            return None, "Authentication failed. Please check your CRUSTDATA_API_KEY."
        elif response.status_code == 400:
            return None, response.json()
        elif response.status_code == 429:
            return None, "Rate limit exceeded. Please try again later."
        else:
            return None, f"API request failed with status {response.status_code}: {response.text}"
            
    except requests.exceptions.Timeout:
        return None, "Request timed out. Please try again."
    except requests.exceptions.ConnectionError:
        return None, "Connection error. Please check your internet connection."
    except Exception as e:
        return None, f"An error occurred while searching LinkedIn profiles: {str(e)}"
